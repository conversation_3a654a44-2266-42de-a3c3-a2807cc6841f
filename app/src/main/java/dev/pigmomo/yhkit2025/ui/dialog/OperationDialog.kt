package dev.pigmomo.yhkit2025.ui.dialog

import android.widget.Toast
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.utils.OrderDataCacheManager

/**
 * 通用操作对话框，用于批量处理任务。
 * @param title 对话框标题。
 * @param operations 可供选择的操作列表。
 * @param onDismiss 对话框关闭时的回调。
 * @param onConfirm 用户点击确认按钮后的回调，返回所选操作和是否启用多线程。
 * @param showMultiThreadDialog 显示多线程配置对话框的回调。
 * @param multiThreadRangeList 多线程范围列表。
 * @param multiThreadEnabled 是否启用多线程。
 * @param setMultiThreadEnabled 设置多线程启用状态的回调。
 * @param fastenAddress 是否固定地址。
 * @param boostCouponHelpData 助力券数据列表。
 */
@Composable
fun OperationDialog(
    title: String,
    operations: List<String>,
    onDismiss: () -> Unit,
    onConfirm: (selectedOps: Set<String>, multiThread: List<Int>, activityGetType: String) -> Unit,
    showMultiThreadDialog: () -> Unit,
    multiThreadRangeList: List<Int>,
    multiThreadEnabled: Boolean,
    setMultiThreadEnabled: (Boolean) -> Unit,
    boostCouponHelpData: List<String> = emptyList(),
    fastenAddress: Boolean,
    onFastenAddressChange: (Boolean) -> Unit,
    activityGetType: String = "1",
    showOrderExportDialog: () -> Unit = {}
) {
    val context = LocalContext.current

    var activityGetType by remember { mutableStateOf<String>(activityGetType) }
    var selectedOperations by remember { mutableStateOf<Set<String>>(emptySet()) }

    // 判断是否符合地址固定的规则和其他条件
    val isOperationValid = { ops: Set<String> ->
        val hasCleanAddressOp = ops.contains("清空地址")
        val requiresFastAddress = ops.any { op ->
            op == "积分组队" || op == "助力券" || op == "邀请有礼" ||
                    op == "查询状态" || op == "查询余额" || op == "查询积分" || op == "查询订单" || op == "清空购物车" || op == "清空订单"
        }
        val hasBoostCouponOp = ops.contains("助力券")

        // 检查规则
        when {
            // 如果选择了清空地址操作且启用了固定地址
            hasCleanAddressOp && fastenAddress -> {
                Toast.makeText(context, "清空地址 不能地址固定", Toast.LENGTH_SHORT).show()
                onFastenAddressChange(false)
                false
            }
            // 如果选择了需要固定地址的操作且未启用固定地址
            !hasCleanAddressOp && requiresFastAddress && !fastenAddress -> {
                Toast.makeText(context, "该操作需要地址固定", Toast.LENGTH_SHORT).show()
                onFastenAddressChange(true)
                false
            }
            // 如果选择了助力券操作、发起活动但助力券数据为空
            hasBoostCouponOp && activityGetType != "2" && boostCouponHelpData.isEmpty() -> {
                Toast.makeText(context, "助力券配置为空", Toast.LENGTH_SHORT).show()
                false
            }
            // 通过所有规则检查
            else -> true
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = dialogContainerColor(),
        title = { Text(title) },
        text = {
            Column {
                // 多线程配置卡片
                Card(
                    colors = cardThemeOverlay(),
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    Row {
                        TokenActionButton(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            text = "多线程",
                            onClick = {
                                if (!multiThreadEnabled) {
                                    showMultiThreadDialog()
                                } else {
                                    setMultiThreadEnabled(false)
                                }
                            },
                            tint = if (!multiThreadEnabled) Color(0xFF48454E).copy(alpha = 0.5f) else Color(
                                0xFF48454E
                            )
                        )
                        // 活动类型按钮
                        if (operations.contains("积分组队")) {
                            TokenActionButton(
                                imageVector = Icons.Filled.Person,
                                text = "参加活动",
                                onClick = {
                                    activityGetType = if (activityGetType == "1") "2" else "1"
                                },
                                tint = if (activityGetType == "1") Color(0xFF48454E).copy(alpha = 0.5f) else Color(
                                    0xFF48454E
                                )
                            )
                        }
                        TokenActionButton(
                            imageVector = Icons.Filled.Lock,
                            text = "地址固定",
                            onClick = { onFastenAddressChange(!fastenAddress) },
                            tint = if (!fastenAddress)
                                Color(0xFF48454E).copy(alpha = 0.5f)
                            else
                                Color(0xFF48454E),
                        )

                        // 导出订单按钮
                        if (operations.contains("查询订单")) {
                            val totalOrderCount = OrderDataCacheManager.getTotalOrderCount()
                            TokenActionButton(
                                icon = R.drawable.baseline_outbound_24,
                                text = "导出订单",
                                onClick = {
                                    if (totalOrderCount == 0) {
                                        Toast.makeText(
                                            context,
                                            "查询订单数据为空",
                                            Toast.LENGTH_SHORT
                                        ).show()
                                        return@TokenActionButton
                                    }

                                    showOrderExportDialog()
                                },
                                tint = if (totalOrderCount > 0) Color(0xFF48454E) else Color(
                                    0xFF48454E
                                ).copy(alpha = 0.5f)
                            )
                        }
                    }
                }

                // 操作选择卡片
                operations.forEach { operation ->
                    val isSelected = selectedOperations.contains(operation)
                    OperationSelectionCard(
                        operationName = operation + if (operation == "助力券" && activityGetType != "2") " ${
                            boostCouponHelpData.joinToString(
                                ","
                            )
                        }" else "",
                        isSelected = isSelected,
                        onClick = {
                            // 计算新的选中操作集合
                            val newSelectedOps = if (isSelected) {
                                selectedOperations - operation
                            } else {
                                selectedOperations + operation
                            }

                            // 检查是否符合固定地址规则
                            if (isOperationValid(newSelectedOps)) {
                                // 规则检查通过，更新选中状态
                                selectedOperations = newSelectedOps
                            }
                            // 如果规则检查不通过，不更新选中状态，并且已经显示了Toast提示
                        }
                    )
                }
            }
        },
        confirmButton = {
            Button(
                enabled = selectedOperations.isNotEmpty(),
                onClick = {
                    // 验证必要条件
                    if (!multiThreadEnabled) {
                        Toast.makeText(context, "请配置多线程", Toast.LENGTH_SHORT).show()
                        return@Button
                    }

                    // 规则在选择操作时已经检查过，这里再检查一次以确保
                    if (!isOperationValid(selectedOperations)) {
                        return@Button
                    }

                    // 条件满足，执行确认操作
                    onConfirm(
                        selectedOperations,
                        multiThreadRangeList,
                        activityGetType
                    )
                    setMultiThreadEnabled(false)
                    onDismiss()
                }) {
                Text("确认")
            }
        },
        dismissButton = {
            Button(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 可选择的操作卡片。
 * @param operationName 操作名称。
 * @param isSelected 是否被选中。
 * @param onClick 点击事件回调。
 */
@Composable
fun OperationSelectionCard(
    operationName: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xCBF7DEF4) else CardContainerColor,
        ),
        modifier = Modifier
            .padding(vertical = 2.dp)
            .height(62.dp)
            .fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxSize()
                .clickable(onClick = onClick)
        ) {
            Column(
                modifier = Modifier
                    .padding(start = 10.dp, end = 8.dp)
            ) {
                Text(
                    text = operationName,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                    modifier = Modifier.horizontalScroll(rememberScrollState())
                )
            }
        }
    }
}