package dev.pigmomo.yhkit2025.ui.screens

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.graphics.lerp
import androidx.core.view.WindowCompat
import android.app.Activity
import androidx.compose.foundation.layout.Arrangement
import kotlin.math.max
import kotlin.math.min
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Call
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.ui.platform.LocalContext
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.widget.Toast
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalContext
import coil.compose.rememberAsyncImagePainter
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.order.OrderDetailData
import dev.pigmomo.yhkit2025.ui.theme.CardContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import dev.pigmomo.yhkit2025.viewmodel.OrderDetailViewModel

/**
 * 订单详情屏幕
 * 显示订单的详细信息，包括状态、商品列表、价格明细等
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OrderDetailScreen(
    viewModel: OrderDetailViewModel,
    orderDetail: OrderDetailData? = null,
    onBackClick: () -> Unit = {}
) {
    // 设置订单详情数据
    LaunchedEffect(orderDetail) {
        orderDetail?.let { viewModel.setOrderDetailData(it) }
    }

    // 观察ViewModel状态
    val orderDetailData by viewModel.orderDetailData.collectAsState()
    val isLoading by viewModel.isLoading
    val toastMessage by viewModel.toastMessage.collectAsState()

    // 滚动状态
    val scrollState = rememberScrollState()
    val density = LocalDensity.current
    val view = LocalView.current

    // 计算滚动进度，用于控制透明度和标题显示
    val scrollProgress = remember {
        derivedStateOf {
            val maxScroll = with(density) { 60.dp.toPx() } // 60dp后完全收起
            min(1f, scrollState.value / maxScroll)
        }
    }

    // 状态栏和标题的透明度
    val topBarAlpha = scrollProgress.value
    val statusAlpha = 1f - scrollProgress.value

    // 动态设置状态栏颜色
    val backgroundColor = Color(0xFFF5F5F5)
    val whiteColor = Color.White

    // 直接计算状态栏颜色，不使用 remember
    val statusBarColor = lerp(backgroundColor, whiteColor, topBarAlpha)

    // 设置状态栏颜色
    if (!view.isInEditMode) {
        LaunchedEffect(statusBarColor) {
            val window = (view.context as Activity).window
            window.statusBarColor = statusBarColor.toArgb()
            // 状态栏图标始终为深色（因为背景是浅色）
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = true
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // 背景色
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF5F5F5))
        )

        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(top = 56.dp) // 为顶部栏留出空间
        ) {
            if (orderDetailData != null) {
                // 订单状态区域（带渐变效果）
                OrderStatusSection(
                    viewModel = viewModel,
                    alpha = statusAlpha
                )

                // 送达照片区域（如果有的话）
                DeliveryPhotosSection(viewModel)

                // 商品列表区域（包含店铺信息）
                ProductListWithShopSection(viewModel)

                // 订单信息区域
                OrderInfoSection(viewModel)

                // 发票区域
                InvoiceSection(viewModel)

                // 为底部按钮留出空间
                Spacer(modifier = Modifier.height(80.dp))
            } else {
                // 显示空状态或加载状态
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text("暂无订单详情数据")
                }
            }
        }

        // 透明顶部栏
        TopAppBar(
            title = {
                Text(
                    text = viewModel.getOrderStatusText(),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black, // 始终为黑色
                    modifier = Modifier.alpha(topBarAlpha).fillMaxWidth(), // 使用平滑的透明度变化
                    textAlign = TextAlign.Center
                )
            },
            navigationIcon = {
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            Color.White.copy(alpha = 0.9f),
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_back_order),
                            contentDescription = "返回",
                            tint = Color.Black, // 始终为黑色
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            },
            actions = {
                // 添加客服按钮
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            Color.White.copy(alpha = 0.9f),
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    IconButton(
                        onClick = {
                            // TODO: 实现客服功能
                        },
                        modifier = Modifier.size(36.dp)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_kefu_order),
                            contentDescription = "客服",
                            tint = Color.Black, // 始终为黑色
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = statusBarColor,
                titleContentColor = Color.Black,
                navigationIconContentColor = Color.Black,
                actionIconContentColor = Color.Black
            ),
            modifier = Modifier.alpha(max(0.3f, topBarAlpha))
        )

        // 固定底部按钮
        if (orderDetailData != null) {
            FixedBottomButtonsSection(
                viewModel = viewModel,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

/**
 * 订单状态区域
 */
@Composable
fun OrderStatusSection(
    viewModel: OrderDetailViewModel,
    alpha: Float = 1f
) {
    Column(
        modifier = Modifier
            .padding(vertical = 16.dp, horizontal = 24.dp)
            .alpha(alpha)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = viewModel.getOrderStatusText(),
                fontSize = 28.sp,
                lineHeight = 28.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )

            if (viewModel.getShowTimeslots()) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "查看详情",
                    tint = Color.Gray,
                    modifier = Modifier.size(20.dp)
                )
            }
        }

        Text(
            text = viewModel.getOrderStatusTitle(),
            fontSize = 15.sp,
            lineHeight = 15.sp,
            color = Color(0xFF666666),
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * 送达照片区域
 */
@Composable
fun DeliveryPhotosSection(viewModel: OrderDetailViewModel) {
    val deliveryPhotos = viewModel.getDeliveryPhotos()
    val deliveryTitle = viewModel.getDeliveryPhotosTitle()

    if (deliveryPhotos.isNotEmpty()) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 4.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Icon(
                            painter = painterResource(id = R.drawable.baseline_assistant_photo_24),
                            contentDescription = "送达照片",
                            tint = Color.Red,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = deliveryTitle,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    Button(
                        onClick = { viewModel.viewDeliveryPhotos() },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.Red,
                            contentColor = Color.White
                        ),
                        modifier = Modifier.height(32.dp),
                        shape = RoundedCornerShape(16.dp)
                    ) {
                        Text(
                            text = "查看图片",
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 * 商品列表区域（包含店铺信息）
 */
@SuppressLint("DefaultLocale")
@Composable
private fun ProductListWithShopSection(viewModel: OrderDetailViewModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column {
            // 店铺信息区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 添加店铺图标
                Image(
                    painter = rememberAsyncImagePainter(viewModel.getShopIcon()),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = viewModel.getShopName(),
                    fontSize = 17.sp,
                    color = Color.Black
                )
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "查看店铺",
                    tint = Color.Gray,
                    modifier = Modifier.size(20.dp)
                )
            }

            // 商品列表区域
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                val productList = viewModel.getProductList()
                val displayProducts = productList.take(3) // 只显示前3个商品
                val remainingCount = productList.size - displayProducts.size

                displayProducts.forEachIndexed { index, product ->
                    ProductItem(
                        imageUrl = product.imgurl,
                        title = product.title,
                        subtitle = product.subtitle,
                        price = String.format("%.2f", (product.price?.value ?: 0) / 100.0),
                        originalPrice = if ((product.price?.lineprice ?: 0) > (product.price?.value
                                ?: 0)
                        ) {
                            String.format("%.2f", (product.price?.lineprice ?: 0) / 100.0)
                        } else null,
                        quantity = "${product.num / 100}${product.unit}",
                        actualPrice = String.format("%.2f", product.actualPaidPrice / 100.0),
                        tag = product.titletag?.text
                    )

                    if (index < displayProducts.size - 1) {
                        Spacer(modifier = Modifier.height(24.dp))
                    }
                }

                if (remainingCount > 0) {
                    // 显示剩余商品数量
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { },
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "还有${remainingCount}种商品",
                            fontSize = 14.sp,
                            color = Color.Gray
                        )
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                            contentDescription = "查看更多",
                            tint = Color.Gray
                        )
                    }
                }
            }
        }

        Column(
            modifier = Modifier.padding(14.dp) // 减少内边距，从16dp改为14dp
        ) {
            val priceDetailList = viewModel.getPriceDetailList()

            // 显示价格详情列表
            priceDetailList.forEach { priceDetail ->
                val valueColor = if (priceDetail.highlight == 1) Color.Red else Color.Black
                val strikethrough =
                    priceDetail.linyAmount ?: ""
                PriceItem(
                    label = priceDetail.title,
                    value = priceDetail.amount,
                    valueColor = valueColor,
                    strikethrough = strikethrough
                )
            }

            // 实付金额
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.Bottom
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "已优惠",
                        fontSize = 15.sp // 减小字体，从14sp改为13sp
                    )
                    Text(
                        text = "¥${viewModel.getTotalDiscount()}",
                        fontSize = 15.sp, // 减小字体，从14sp改为13sp
                        color = Color.Red,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = "实付¥",
                        fontWeight = FontWeight.Medium,
                        fontSize = 15.sp
                    )
                    Text(
                        text = viewModel.getTotalPayment(),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }
            }
        }
    }
}

/**
 * 商品项组件
 */
@Composable
private fun ProductItem(
    imageUrl: String,
    title: String,
    subtitle: String = "",
    price: String,
    originalPrice: String? = null,
    quantity: String,
    actualPrice: String,
    tag: String? = null,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        // 商品图片
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color.LightGray)
        ) {
            if (imageUrl.isNotEmpty()) {
                Image(
                    painter = rememberAsyncImagePainter(imageUrl),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // 使用默认图标
                Icon(
                    painter = painterResource(id = R.drawable.baseline_egg_alt_24),
                    contentDescription = null,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            // 商品标签和标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 标签（如秒杀）
                tag?.let {
                    Box(
                        modifier = Modifier
                            .background(
                                Color.Red,
                                shape = RoundedCornerShape(2.dp)
                            )
                            .padding(horizontal = 4.dp, vertical = 1.dp)
                    ) {
                        Text(
                            text = it,
                            fontSize = 10.sp,
                            color = Color.White
                        )
                    }
                    Spacer(modifier = Modifier.width(4.dp))
                }

                // 商品标题
                Text(
                    text = title,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 商品数量和价格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "单价：¥$price",
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "数量：$quantity",
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }

        Spacer(modifier = Modifier.width(8.dp))

        // 实付价格
        Column(horizontalAlignment = Alignment.End) {
            Text(
                text = "实付：¥$actualPrice",
                fontSize = 14.sp,
                lineHeight = 14.sp,
                fontWeight = FontWeight.Medium
            )
            originalPrice?.let {
                Text(
                    text = "¥$it",
                    fontSize = 14.sp,
                    lineHeight = 14.sp,
                    color = Color.Gray,
                    textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough
                )
            }
            //售后按钮
            Button(
                onClick = { },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Red,
                    contentColor = Color.White
                )
            ) {
                Text(text = "申请售后")
            }
        }
    }
}

/**
 * 价格项组件
 */
@Composable
private fun PriceItem(
    label: String,
    value: String,
    valueColor: Color = Color.Black,
    strikethrough: String = ""
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp), // 减少垂直间距，从6dp改为4dp
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 16.sp,
            lineHeight = 16.sp,
            color = Color(0xFF666666)
        )

        Row(verticalAlignment = Alignment.CenterVertically) {
            if (strikethrough.isNotEmpty()) {
                Text(
                    text = strikethrough,
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = Color(0xFF999999),
                    textDecoration = androidx.compose.ui.text.style.TextDecoration.LineThrough,
                    modifier = Modifier.padding(end = 4.dp)
                )
            }
            Text(
                text = value,
                fontSize = 16.sp, // 减小字体，从14sp改为13sp
                color = valueColor,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

/**
 * 订单信息区域
 */
@Composable
private fun OrderInfoSection(viewModel: OrderDetailViewModel) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 12.dp, vertical = 4.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 预约时间
            InfoItem("预约时间", viewModel.getExpectedTime())

            // 收货信息 - 分两行显示
            val deliveryAddress = viewModel.getDeliveryAddress()
            val receiverInfo = viewModel.getReceiverInfo()
            InfoItem("收货信息", deliveryAddress, isMultiLine = true)
            if (receiverInfo.isNotEmpty() && receiverInfo != "未知收货人") {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    horizontalArrangement = Arrangement.End
                ) {
                    Text(
                        text = receiverInfo,
                        fontSize = 15.sp,
                        lineHeight = 15.sp,
                        color = Color(0xFF333333),
                        modifier = Modifier.padding(start = 80.dp)
                    )
                }
            }

            // 配送员信息 - 带金牌标识
            val carrierInfo = viewModel.getCarrierInfo()
            if (carrierInfo.isNotEmpty()) {
                InfoItemWithIcon("配送员", carrierInfo, hasIcon = false)
            }

            // 订单编号 - 带复制功能
            InfoItemWithCopy("订单编号", viewModel.getOrderId())

            // 下单时间
            InfoItem("下单时间", viewModel.getOrderTime())

            // 支付方式
            InfoItem("支付方式", viewModel.getPaymentMethod())

            // 订单备注
            val orderComment = viewModel.getOrderComment()
            if (orderComment.isNotEmpty()) {
                InfoItem("订单备注", orderComment)
            }

            // 缺货信息
            val outOfStockMsg = viewModel.getOutOfStockMessage()
            if (outOfStockMsg.isNotEmpty()) {
                InfoItem("缺货处理方式", outOfStockMsg)
            }
        }
    }
}

/**
 * 信息项组件
 */
@Composable
private fun InfoItem(
    label: String,
    value: String,
    isMultiLine: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = if (isMultiLine) Alignment.Top else Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 15.sp,
            color = Color(0xFF666666)
        )

        Spacer(modifier = Modifier.width(16.dp))

        Text(
            text = value,
            fontSize = 15.sp,
            color = Color(0xFF333333),
            textAlign = TextAlign.End
        )
    }
}

/**
 * 带图标的信息项组件（用于配送员信息）
 */
@Composable
private fun InfoItemWithIcon(
    label: String,
    value: String,
    hasIcon: Boolean = false
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 15.sp,
            color = Color(0xFF666666),
            modifier = Modifier.width(80.dp)
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = value,
                fontSize = 15.sp,
                color = Color(0xFF333333),
                textAlign = TextAlign.End
            )

            if (hasIcon) {
                Spacer(modifier = Modifier.width(4.dp))
                // 金牌图标 - 使用简单的圆形背景代替
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .background(
                            Color(0xFFFFD700),
                            CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "金",
                        fontSize = 10.sp,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}

/**
 * 带复制功能的信息项组件（用于订单编号）
 */
@Composable
private fun InfoItemWithCopy(
    label: String,
    value: String
) {
    val context = LocalContext.current

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 6.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 15.sp,
            color = Color(0xFF666666),
            modifier = Modifier.width(80.dp)
        )

        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = value,
                fontSize = 15.sp,
                color = Color(0xFF333333),
                textAlign = TextAlign.End
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 复制按钮
            Text(
                text = "复制",
                fontSize = 13.sp,
                color = Color(0xFF007AFF),
                modifier = Modifier
                    .clickable {
                        // 复制到剪贴板
                        val clipboard =
                            context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val clip = ClipData.newPlainText("订单编号", value)
                        clipboard.setPrimaryClip(clip)
                        Toast.makeText(context, "已复制到剪贴板", Toast.LENGTH_SHORT).show()
                    }
                    .padding(horizontal = 4.dp, vertical = 2.dp)
            )
        }
    }
}

/**
 * 发票区域
 */
@Composable
private fun InvoiceSection(viewModel: OrderDetailViewModel) {
    val invoiceInfo = viewModel.getInvoiceInfo()

    if (invoiceInfo.isNotEmpty()) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp, vertical = 4.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { viewModel.openInvoice() }
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "发票",
                    fontSize = 14.sp,
                    color = Color.Gray
                )

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = invoiceInfo,
                        fontSize = 14.sp
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                        contentDescription = "查看发票",
                        tint = Color.Gray
                    )
                }
            }
        }
    }
}

/**
 * 固定底部按钮区域
 */
@Composable
private fun FixedBottomButtonsSection(
    viewModel: OrderDetailViewModel,
    modifier: Modifier = Modifier
) {
    val allButtons = viewModel.getStatusButtons()
    // 过滤掉删除订单按钮（actiontype=16），只保留其他按钮
    val buttons = allButtons.filter { it.actiontype != 16 }

    if (buttons.isNotEmpty()) {
        // 固定在底部的按钮区域
        Surface(
            modifier = modifier
                .fillMaxWidth(),
            color = Color.White,
            shadowElevation = 8.dp
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.End,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 只显示前2个重要按钮，符合截图样式
                buttons.take(2).forEachIndexed { index, button ->
                    Button(
                        onClick = {
                            when (button.actiontype) {
                                2 -> viewModel.showToast("评价功能待实现") // 评价
                                3 -> viewModel.reorder() // 再来一单
                                9 -> viewModel.showToast("申请售后功能待实现") // 申请售后
                                40 -> viewModel.showToast("打赏骑手功能待实现") // 打赏骑手
                                else -> viewModel.showToast("功能待实现")
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (button.highlight == 1) Color.Red else Color(
                                0xFFF5F5F5
                            ),
                            contentColor = if (button.highlight == 1) Color.White else Color.Black
                        ),
                        modifier = Modifier
                            .padding(start = if (index > 0) 12.dp else 0.dp)
                            .height(40.dp),
                        shape = RoundedCornerShape(20.dp)
                    ) {
                        Text(
                            text = button.actionname,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(horizontal = 16.dp)
                        )
                    }
                }
            }
        }
    }
}